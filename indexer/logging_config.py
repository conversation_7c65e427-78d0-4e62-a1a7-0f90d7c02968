"""
Configuração do sistema de logging com Loguru para o Kafka Data Indexer.

Este módulo configura o loguru para logging apenas no console, adequado para ambientes Kubernetes.
"""

import os
import sys
from loguru import logger
from typing import Optional


def configure_loguru():
    """
    Configura o loguru para logging apenas no console.

    Variáveis de ambiente suportadas:
    - LOG_LEVEL: Nível de log (TRACE, DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL)
    - LOG_FORMAT: Formato de log (simple, detailed)
    """

    # Remove o handler padrão do loguru
    logger.remove()

    # Configurações baseadas em variáveis de ambiente
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    log_format = os.getenv('LOG_FORMAT', 'detailed').lower()

    # Definir formatos
    formats = {
        'simple': "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name} | {message}",
        'detailed': "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}"
    }

    # Usar formato padrão se não encontrado
    format_string = formats.get(log_format, formats['detailed'])

    # Configurar handler para console (stdout)
    logger.add(
        sys.stdout,
        format=format_string,
        level=log_level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )


def get_logger(name: Optional[str] = None):
    """
    Retorna uma instância do logger configurado.
    
    Args:
        name: Nome do logger (opcional). Se não fornecido, usa o nome do módulo chamador.
        
    Returns:
        Logger configurado do loguru
    """
    if name:
        return logger.bind(name=name)
    else:
        # Tenta obter o nome do módulo chamador
        import inspect
        frame = inspect.currentframe()
        try:
            caller_frame = frame.f_back
            caller_module = caller_frame.f_globals.get('__name__', 'unknown')
            # Simplifica o nome do módulo para ficar mais legível
            if caller_module.startswith('indexer.'):
                caller_module = caller_module.replace('indexer.', '')
            return logger.bind(name=caller_module)
        finally:
            del frame


# Configurar o loguru na importação do módulo
configure_loguru()

# Exportar o logger configurado
__all__ = ['logger', 'get_logger', 'configure_loguru']
