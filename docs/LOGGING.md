# Sistema de Logging com Loguru

Este documento descreve como usar o sistema de logging baseado no Loguru na aplicação Kafka Data Indexer.

## ✅ Status da Implementação

O sistema de logging com Loguru foi **totalmente implementado** e está funcionando. Todas as classes e módulos foram migrados do sistema antigo (print statements) para o loguru.

### Arquivos Modificados

- ✅ `indexer/logging_config.py` - Módulo de configuração do loguru (criado)
- ✅ `indexer/processors/__init__.py` - AbstractProcessor e KeepsMessage migrados
- ✅ `indexer/worker.py` - IndexerWorker migrado
- ✅ `indexer/engine/base_processor.py` - BaseProcessor migrado
- ✅ `indexer/engine/log_processor.py` - LogProcessor migrado
- ✅ `indexer/engine/worker.py` - Worker do engine migrado
- ✅ `requirements.txt` - Loguru adicionado

## Configuração

### Variáveis de Ambiente

Configure as seguintes variáveis de ambiente para personalizar o comportamento do logging:

```bash
# Nível de log (TRACE, DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Formato de log (simple, detailed)
LOG_FORMAT=detailed

# Arquivo de log principal (opcional)
LOG_FILE=/var/log/indexer/app.log

# Arquivo de log de erros (opcional)
ERROR_LOG_FILE=/var/log/indexer/error.log
```

### Formatos Disponíveis

#### Simple
```
2025-01-05 14:30:25 | INFO     | IndexerWorker | Starting batch processing...
```

#### Detailed (padrão)
```
2025-01-05 14:30:25.123 | INFO     | IndexerWorker:start:91 | Starting batch processing...
```

## Uso nos Processadores

### Logging Básico

```python
class MyProcessor(AbstractProcessor):
    def do_process_batch(self, batch, updated_ids):
        # Log de informação (método herdado)
        self.log("Processing batch started")
        
        # Log com nível específico
        self.log("Debug information", level="DEBUG")
        self.log("Warning message", level="WARNING")
        self.log("Error occurred", level="ERROR")
        
        # Compatibilidade com sistema antigo
        self.log("Error occurred", error=True)  # Equivale a level="ERROR"
        
        return True
```

### Logging Avançado

Para casos mais complexos, você pode usar o logger diretamente:

```python
from indexer.logging_config import get_logger

class MyProcessor(AbstractProcessor):
    def __init__(self):
        super().__init__()
        # O logger já está disponível como self.logger
        
    def complex_operation(self):
        try:
            # Operação complexa
            result = self.some_operation()
            self.logger.success(f"Operation completed successfully: {result}")
        except Exception as e:
            self.logger.exception(f"Operation failed: {e}")
            raise
```

## Níveis de Log

- **TRACE**: Informações muito detalhadas para debugging
- **DEBUG**: Informações de debug para desenvolvimento
- **INFO**: Informações gerais sobre o funcionamento
- **SUCCESS**: Operações completadas com sucesso
- **WARNING**: Avisos que não impedem o funcionamento
- **ERROR**: Erros que podem afetar o funcionamento
- **CRITICAL**: Erros críticos que podem parar a aplicação

## Migração do Sistema Anterior

### Antes (print/logging antigo)
```python
print(f"[{datetime.now()}] Processing {len(batch)} messages")
self.log(f"Error: {str(e)}", error=True)
```

### Depois (Loguru)
```python
self.log(f"Processing {len(batch)} messages")
self.log(f"Error: {str(e)}", level="ERROR")
# ou mantendo compatibilidade:
self.log(f"Error: {str(e)}", error=True)
```

## Configuração em Produção

### Docker Compose
```yaml
services:
  indexer:
    environment:
      - LOG_LEVEL=INFO
      - LOG_FORMAT=detailed
      - LOG_FILE=/app/logs/indexer.log
      - ERROR_LOG_FILE=/app/logs/error.log
    volumes:
      - ./logs:/app/logs
```

### Kubernetes
```yaml
env:
  - name: LOG_LEVEL
    value: "INFO"
  - name: LOG_FORMAT
    value: "detailed"
  - name: LOG_FILE
    value: "/var/log/indexer/app.log"
```

## Benefícios do Loguru

1. **Formatação automática**: Timestamps, cores e formatação automática
2. **Rotação de arquivos**: Rotação automática por tamanho e tempo
3. **Compressão**: Compressão automática de logs antigos
4. **Estruturação**: Logs bem estruturados e legíveis
5. **Performance**: Melhor performance que o logging padrão do Python
6. **Facilidade de uso**: API mais simples e intuitiva
7. **Compatibilidade**: Mantém compatibilidade com código existente

## Troubleshooting

### Logs não aparecem
- Verifique se `LOG_LEVEL` está configurado corretamente
- Certifique-se de que o diretório de logs existe e tem permissões de escrita

### Performance
- Use `LOG_LEVEL=WARNING` ou superior em produção para reduzir I/O
- Configure rotação adequada para evitar arquivos muito grandes

### Debugging
- Use `LOG_LEVEL=DEBUG` para ver informações detalhadas
- Use `LOG_FORMAT=detailed` para ver função e linha do código

## Testes

Para testar a configuração do loguru, execute:

```bash
# Teste básico
python test_loguru_direct.py

# Exemplo de uso
python example_usage.py
```

## Exemplos Práticos

Veja o arquivo `example_usage.py` para exemplos completos de como usar o sistema de logging em diferentes cenários.
