#!/usr/bin/env python3
"""
Script de teste direto para verificar apenas o módulo logging_config.
"""

import os
import sys

# Adicionar o diretório do projeto ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_logging_config_direct():
    """Testa diretamente o módulo de configuração do loguru."""
    print("=== Testando módulo logging_config diretamente ===")
    
    try:
        # Importar diretamente o módulo sem passar pelo __init__.py
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
        import logging_config
        
        # Teste com logger padrão
        logging_config.logger.info("Teste do logger padrão")
        logging_config.logger.warning("Teste de warning")
        logging_config.logger.error("Teste de error")
        logging_config.logger.success("Teste de success")
        
        # Teste com logger nomeado
        test_logger = logging_config.get_logger("TestLogger")
        test_logger.info("Teste do logger nomeado")
        test_logger.debug("Teste de debug (pode não aparecer dependendo do nível)")
        
        print("✅ Módulo logging_config funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no módulo logging_config: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_variables():
    """Testa diferentes configurações via variáveis de ambiente."""
    print("\n=== Testando configurações via variáveis de ambiente ===")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
        import logging_config
        
        # Teste com formato simples
        os.environ['LOG_FORMAT'] = 'simple'
        os.environ['LOG_LEVEL'] = 'DEBUG'
        
        # Recarregar a configuração
        logging_config.configure_loguru()
        
        test_logger = logging_config.get_logger("EnvTest")
        test_logger.debug("Teste com formato simples e nível DEBUG")
        test_logger.info("Teste de info com formato simples")
        
        # Teste com formato JSON
        os.environ['LOG_FORMAT'] = 'json'
        logging_config.configure_loguru()
        
        json_logger = logging_config.get_logger("JSONTest")
        json_logger.info("Teste com formato JSON")
        json_logger.warning("Warning em formato JSON")
        
        print("✅ Configurações via variáveis de ambiente funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro nas configurações de ambiente: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Limpar variáveis de ambiente
        os.environ.pop('LOG_FORMAT', None)
        os.environ.pop('LOG_LEVEL', None)

def test_different_formats():
    """Testa diferentes formatos de log."""
    print("\n=== Testando diferentes formatos ===")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
        import logging_config
        
        formats = ['simple', 'detailed', 'json']
        
        for fmt in formats:
            print(f"\n--- Testando formato: {fmt} ---")
            os.environ['LOG_FORMAT'] = fmt
            os.environ['LOG_LEVEL'] = 'INFO'
            
            logging_config.configure_loguru()
            
            test_logger = logging_config.get_logger(f"Format{fmt.title()}Test")
            test_logger.info(f"Teste com formato {fmt}")
            test_logger.warning(f"Warning com formato {fmt}")
            test_logger.success(f"Success com formato {fmt}")
        
        print("\n✅ Todos os formatos funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro nos formatos: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Limpar variáveis de ambiente
        os.environ.pop('LOG_FORMAT', None)
        os.environ.pop('LOG_LEVEL', None)

def test_file_logging():
    """Testa logging para arquivo."""
    print("\n=== Testando logging para arquivo ===")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
        import logging_config
        
        # Configurar para escrever em arquivo
        test_log_file = "/tmp/test_indexer.log"
        os.environ['LOG_FILE'] = test_log_file
        os.environ['LOG_FORMAT'] = 'detailed'
        os.environ['LOG_LEVEL'] = 'INFO'
        
        logging_config.configure_loguru()
        
        file_logger = logging_config.get_logger("FileTest")
        file_logger.info("Teste de log para arquivo")
        file_logger.warning("Warning para arquivo")
        file_logger.error("Error para arquivo")
        
        # Verificar se o arquivo foi criado
        if os.path.exists(test_log_file):
            with open(test_log_file, 'r') as f:
                content = f.read()
                if "Teste de log para arquivo" in content:
                    print("✅ Logging para arquivo funcionando!")
                    # Limpar arquivo de teste
                    os.remove(test_log_file)
                    return True
                else:
                    print("❌ Conteúdo não encontrado no arquivo de log")
                    return False
        else:
            print("❌ Arquivo de log não foi criado")
            return False
        
    except Exception as e:
        print(f"❌ Erro no logging para arquivo: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Limpar variáveis de ambiente
        os.environ.pop('LOG_FILE', None)
        os.environ.pop('LOG_FORMAT', None)
        os.environ.pop('LOG_LEVEL', None)

def main():
    """Executa todos os testes."""
    print("🧪 Iniciando testes diretos da configuração do loguru...\n")
    
    tests = [
        test_logging_config_direct,
        test_environment_variables,
        test_different_formats,
        test_file_logging
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print(f"\n📊 Resultados: {sum(results)}/{len(results)} testes passaram")
    
    if all(results):
        print("🎉 Todos os testes passaram! A configuração do loguru está funcionando corretamente.")
        return 0
    else:
        print("⚠️  Alguns testes falharam. Verifique a configuração.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
