# Migração para Loguru - Kafka Data Indexer

## 📋 Resumo da Implementação

Este documento descreve a migração completa do sistema de logging do projeto Kafka Data Indexer do uso de `print()` statements para o **Loguru**.

## ✅ Status: CONCLUÍDO

A migração foi **100% concluída** e testada. Todos os componentes do projeto agora usam o loguru para logging.

## 🔧 Mudanças Implementadas

### 1. Dependências
- ✅ Adicionado `loguru==0.7.2` ao `requirements.txt`

### 2. Novo Módulo de Configuração
- ✅ Criado `indexer/logging_config.py`
  - Configuração centralizada do loguru
  - Suporte a variáveis de ambiente
  - Múltiplos formatos (simple, detailed)
  - Logging para arquivo com rotação automática

### 3. Classes Migradas

#### AbstractProcessor (`indexer/processors/__init__.py`)
- ✅ Adicionado `self.logger` no construtor
- ✅ Método `log()` migrado para usar loguru
- ✅ Mantida compatibilidade com parâmetro `error=True`
- ✅ Suporte a níveis de log específicos

#### KeepsMessage (`indexer/processors/__init__.py`)
- ✅ Método `log()` migrado para usar loguru
- ✅ Formatação melhorada das mensagens

#### IndexerWorker (`indexer/worker.py`)
- ✅ Adicionado `self.logger` no construtor
- ✅ Método `_log()` migrado para usar loguru
- ✅ Tratamento de erros melhorado

#### BaseProcessor (`indexer/engine/base_processor.py`)
- ✅ Adicionado `self.logger` no construtor
- ✅ Método `log()` migrado para usar loguru

#### LogProcessor (`indexer/engine/log_processor.py`)
- ✅ Migrado para usar loguru
- ✅ Adicionado `self.logger` no construtor

#### Worker Engine (`indexer/engine/worker.py`)
- ✅ Adicionado `self.logger` no construtor
- ✅ Método `_log()` migrado para usar loguru
- ✅ Tratamento de erros melhorado

## 🎯 Funcionalidades Implementadas

### Configuração via Variáveis de Ambiente
```bash
LOG_LEVEL=INFO          # Nível de log
LOG_FORMAT=detailed     # Formato (simple, detailed)
LOG_FILE=/path/to/app.log        # Arquivo principal (opcional)
ERROR_LOG_FILE=/path/to/error.log # Arquivo de erros (opcional)
```

### Formatos de Log
- **Simple**: `2025-01-05 14:30:25 | INFO | ProcessorName | Message`
- **Detailed**: `2025-01-05 14:30:25.123 | INFO | ProcessorName:method:line | Message`

### Níveis de Log Suportados
- TRACE, DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL

### Recursos Avançados
- ✅ Rotação automática de arquivos (100MB)
- ✅ Compressão de logs antigos (.gz)
- ✅ Retenção configurável (30 dias para logs, 60 para erros)
- ✅ Cores no terminal
- ✅ Backtrace e diagnóstico em erros

## 🔄 Compatibilidade

### Método `log()` - Compatibilidade Total
```python
# Funcionam exatamente como antes:
self.log("Mensagem de info")
self.log("Mensagem de erro", error=True)

# Novas funcionalidades:
self.log("Debug info", level="DEBUG")
self.log("Warning", level="WARNING")
self.log("Success", level="SUCCESS")
```

### Logger Direto
```python
# Para uso avançado:
self.logger.info("Info message")
self.logger.error("Error message")
self.logger.success("Success message")
self.logger.exception("Error with traceback")
```

## 🧪 Testes Implementados

### Arquivos de Teste
- ✅ `test_loguru_direct.py` - Testa configuração básica
- ✅ `example_usage.py` - Exemplos práticos de uso

### Cenários Testados
- ✅ Configuração básica do loguru
- ✅ Diferentes formatos de log
- ✅ Logging para arquivo
- ✅ Variáveis de ambiente
- ✅ Compatibilidade com código existente
- ✅ Rotação de arquivos
- ✅ Diferentes níveis de log

## 🚀 Como Usar

### 1. Instalação
```bash
pip install -r requirements.txt
```

### 2. Configuração (Opcional)
```bash
export LOG_LEVEL=INFO
export LOG_FORMAT=detailed
export LOG_FILE=/var/log/indexer/app.log
```

### 3. Uso em Processadores
```python
class MyProcessor(AbstractProcessor):
    def do_process_batch(self, batch, updated_ids):
        self.log("Starting batch processing")
        
        try:
            # Processamento...
            self.log("Batch processed successfully", level="SUCCESS")
        except Exception as e:
            self.log(f"Error processing batch: {e}", error=True)
            raise
```

## 📊 Benefícios da Migração

### Antes (Print Statements)
```python
print(f"[{datetime.now()}] [ProcessorName] Processing batch...")
```

### Depois (Loguru)
```python
self.log("Processing batch...")
```

### Vantagens
1. **Formatação Automática**: Timestamps e formatação consistente
2. **Níveis de Log**: DEBUG, INFO, WARNING, ERROR, etc.
3. **Cores**: Logs coloridos no terminal para melhor legibilidade
4. **Arquivos**: Logging automático para arquivos com rotação
5. **Performance**: Melhor performance que print statements
6. **Estruturação**: Logs bem estruturados e pesquisáveis
7. **Configuração**: Controle via variáveis de ambiente
8. **Compatibilidade**: Código existente continua funcionando

## 🔍 Verificação

Para verificar se a migração está funcionando:

```bash
# Teste básico
python test_loguru_direct.py

# Exemplo completo
python example_usage.py

# Teste com diferentes configurações
LOG_FORMAT=simple python example_usage.py
LOG_LEVEL=DEBUG python example_usage.py
```

## 📝 Próximos Passos

A migração está **completa e funcional**. Recomendações:

1. ✅ **Testar em ambiente de desenvolvimento**
2. ✅ **Configurar variáveis de ambiente apropriadas**
3. ✅ **Documentar configurações específicas do projeto**
4. ✅ **Treinar equipe no novo sistema**

## 🆘 Suporte

- Documentação completa: `docs/LOGGING.md`
- Exemplos práticos: `example_usage.py`
- Testes: `test_loguru_direct.py`

A migração mantém **100% de compatibilidade** com o código existente enquanto adiciona funcionalidades avançadas de logging.
