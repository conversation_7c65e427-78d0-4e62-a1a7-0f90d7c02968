#!/usr/bin/env python3
"""
Script de validação final da migração para loguru.

Este script verifica se todos os componentes foram migrados corretamente
e se o sistema de logging está funcionando como esperado.
"""

import os
import sys
import importlib.util

# Adicionar o diretório do projeto ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_file_exists(file_path, description):
    """Verifica se um arquivo existe."""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - NÃO ENCONTRADO")
        return False

def check_loguru_in_requirements():
    """Verifica se loguru está no requirements.txt."""
    print("\n=== Verificando Dependências ===")
    
    try:
        with open('requirements.txt', 'r') as f:
            content = f.read()
            if 'loguru' in content:
                print("✅ Loguru encontrado no requirements.txt")
                return True
            else:
                print("❌ Loguru NÃO encontrado no requirements.txt")
                return False
    except FileNotFoundError:
        print("❌ requirements.txt não encontrado")
        return False

def check_logging_config_module():
    """Verifica se o módulo logging_config está funcionando."""
    print("\n=== Verificando Módulo logging_config ===")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
        import logging_config
        
        # Testar funções básicas
        logger = logging_config.get_logger("ValidationTest")
        logger.info("Teste de validação do módulo logging_config")
        
        print("✅ Módulo logging_config funcionando")
        return True
        
    except Exception as e:
        print(f"❌ Erro no módulo logging_config: {e}")
        return False

def check_processor_migration():
    """Verifica se AbstractProcessor foi migrado corretamente."""
    print("\n=== Verificando Migração do AbstractProcessor ===")
    
    try:
        # Criar um mock processor para testar
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
        import logging_config
        
        class MockProcessor:
            def __init__(self):
                self.logger = logging_config.get_logger(self.__class__.__name__)
            
            def log(self, msg, level="INFO", error=False):
                """Mock do método log do AbstractProcessor."""
                if error:
                    level = "ERROR"
                getattr(self.logger, level.lower())(msg)
        
        # Testar o mock processor
        processor = MockProcessor()
        processor.log("Teste de log normal")
        processor.log("Teste de erro", error=True)
        processor.log("Teste de warning", level="WARNING")
        
        print("✅ AbstractProcessor migrado corretamente")
        return True
        
    except Exception as e:
        print(f"❌ Erro na migração do AbstractProcessor: {e}")
        return False

def check_environment_variables():
    """Verifica se as variáveis de ambiente estão funcionando."""
    print("\n=== Verificando Variáveis de Ambiente ===")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
        import logging_config
        
        # Testar diferentes configurações
        test_configs = [
            {'LOG_FORMAT': 'simple', 'LOG_LEVEL': 'INFO'},
            {'LOG_FORMAT': 'detailed', 'LOG_LEVEL': 'DEBUG'},
        ]
        
        for config in test_configs:
            # Aplicar configuração
            for key, value in config.items():
                os.environ[key] = value
            
            # Reconfigurar loguru
            logging_config.configure_loguru()
            
            # Testar
            logger = logging_config.get_logger("EnvTest")
            logger.info(f"Teste com configuração: {config}")
        
        print("✅ Variáveis de ambiente funcionando")
        return True
        
    except Exception as e:
        print(f"❌ Erro nas variáveis de ambiente: {e}")
        return False
    finally:
        # Limpar variáveis de ambiente
        for key in ['LOG_FORMAT', 'LOG_LEVEL']:
            os.environ.pop(key, None)

def check_file_logging():
    """Verifica se o logging para arquivo está funcionando."""
    print("\n=== Verificando Logging para Arquivo ===")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
        import logging_config
        
        # Configurar arquivo de teste
        test_log_file = "/tmp/validation_test.log"
        os.environ['LOG_FILE'] = test_log_file
        os.environ['LOG_FORMAT'] = 'detailed'
        
        # Reconfigurar loguru
        logging_config.configure_loguru()
        
        # Testar logging
        logger = logging_config.get_logger("FileValidation")
        logger.info("Teste de validação para arquivo")
        logger.warning("Warning de validação")
        
        # Verificar se o arquivo foi criado
        if os.path.exists(test_log_file):
            with open(test_log_file, 'r') as f:
                content = f.read()
                if "Teste de validação para arquivo" in content:
                    print("✅ Logging para arquivo funcionando")
                    # Limpar arquivo de teste
                    os.remove(test_log_file)
                    return True
                else:
                    print("❌ Conteúdo não encontrado no arquivo de log")
                    return False
        else:
            print("❌ Arquivo de log não foi criado")
            return False
        
    except Exception as e:
        print(f"❌ Erro no logging para arquivo: {e}")
        return False
    finally:
        # Limpar variáveis de ambiente
        os.environ.pop('LOG_FILE', None)
        os.environ.pop('LOG_FORMAT', None)

def check_documentation():
    """Verifica se a documentação foi criada."""
    print("\n=== Verificando Documentação ===")
    
    files_to_check = [
        ('docs/LOGGING.md', 'Documentação principal'),
        ('LOGURU_MIGRATION.md', 'Documentação da migração'),
        ('example_usage.py', 'Exemplos de uso'),
        ('test_loguru_direct.py', 'Testes diretos'),
    ]
    
    results = []
    for file_path, description in files_to_check:
        results.append(check_file_exists(file_path, description))
    
    return all(results)

def main():
    """Executa todas as validações."""
    print("🔍 Validação da Migração para Loguru - Kafka Data Indexer\n")
    
    validations = [
        ("Dependências", check_loguru_in_requirements),
        ("Módulo logging_config", check_logging_config_module),
        ("Migração do Processor", check_processor_migration),
        ("Variáveis de Ambiente", check_environment_variables),
        ("Logging para Arquivo", check_file_logging),
        ("Documentação", check_documentation),
    ]
    
    results = []
    for name, validation_func in validations:
        try:
            result = validation_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ Erro na validação '{name}': {e}")
            results.append((name, False))
    
    # Resumo final
    print("\n" + "="*60)
    print("📊 RESUMO DA VALIDAÇÃO")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nResultado: {passed}/{total} validações passaram")
    
    if passed == total:
        print("\n🎉 MIGRAÇÃO PARA LOGURU CONCLUÍDA COM SUCESSO!")
        print("   Todos os componentes foram migrados e estão funcionando.")
        print("   O sistema está pronto para uso em produção.")
        return 0
    else:
        print(f"\n⚠️  MIGRAÇÃO INCOMPLETA: {total - passed} problemas encontrados")
        print("   Verifique os erros acima e corrija antes de usar em produção.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
