#!/usr/bin/env python3
"""
Exemplo de uso do sistema de logging com loguru no projeto Kafka Data Indexer.

Este arquivo demonstra como usar o novo sistema de logging em diferentes cenários.
"""

import os
import sys

# Adicionar o diretório do projeto ao path para importações
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def example_basic_usage():
    """Exemplo de uso básico do logging."""
    print("=== Exemplo de Uso Básico ===")
    
    # Importar diretamente o módulo de configuração
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
    import logging_config
    
    # Obter um logger
    logger = logging_config.get_logger("ExampleProcessor")
    
    # Diferentes níveis de log
    logger.trace("Informação muito detalhada para debugging")
    logger.debug("Informação de debug")
    logger.info("Informação geral")
    logger.success("Operação completada com sucesso")
    logger.warning("Aviso importante")
    logger.error("Erro que pode afetar o funcionamento")
    logger.critical("Erro crítico")

def example_processor_style():
    """Exemplo simulando o uso em um processador."""
    print("\n=== Exemplo de Uso em Processador ===")
    
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
    import logging_config
    
    class ExampleProcessor:
        def __init__(self):
            self.logger = logging_config.get_logger(self.__class__.__name__)
        
        def log(self, msg, level="INFO", error=False):
            """Método log compatível com AbstractProcessor."""
            if error:
                level = "ERROR"
            getattr(self.logger, level.lower())(msg)
        
        def process_batch(self, batch_size=10):
            """Simula o processamento de um batch."""
            self.log(f"Iniciando processamento de batch com {batch_size} mensagens")
            
            try:
                # Simular processamento
                for i in range(batch_size):
                    if i % 3 == 0:
                        self.log(f"Processando mensagem {i+1}", level="DEBUG")
                    elif i % 5 == 0:
                        self.log(f"Mensagem especial {i+1}", level="SUCCESS")
                
                self.log(f"Batch processado com sucesso: {batch_size} mensagens")
                
            except Exception as e:
                self.log(f"Erro no processamento: {e}", error=True)
                raise
    
    # Usar o processador
    processor = ExampleProcessor()
    processor.process_batch(15)

def example_different_formats():
    """Exemplo de diferentes formatos de log."""
    print("\n=== Exemplo de Diferentes Formatos ===")
    
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
    import logging_config
    
    formats = ['simple', 'detailed']
    
    for fmt in formats:
        print(f"\n--- Formato: {fmt} ---")
        
        # Configurar formato
        os.environ['LOG_FORMAT'] = fmt
        os.environ['LOG_LEVEL'] = 'INFO'
        
        # Reconfigurar loguru
        logging_config.configure_loguru()
        
        # Obter logger e testar
        logger = logging_config.get_logger(f"Format{fmt.title()}")
        logger.info(f"Exemplo com formato {fmt}")
        logger.warning(f"Warning com formato {fmt}")
        logger.success(f"Success com formato {fmt}")

def example_file_logging():
    """Exemplo de logging para arquivo."""
    print("\n=== Exemplo de Logging para Arquivo ===")
    
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
    import logging_config
    
    # Configurar logging para arquivo
    log_file = "/tmp/example_indexer.log"
    error_log_file = "/tmp/example_indexer_errors.log"
    
    os.environ['LOG_FILE'] = log_file
    os.environ['ERROR_LOG_FILE'] = error_log_file
    os.environ['LOG_FORMAT'] = 'detailed'
    os.environ['LOG_LEVEL'] = 'DEBUG'
    
    # Reconfigurar loguru
    logging_config.configure_loguru()
    
    # Obter logger e testar
    logger = logging_config.get_logger("FileExample")
    
    logger.debug("Debug message - vai para arquivo principal")
    logger.info("Info message - vai para arquivo principal")
    logger.warning("Warning message - vai para arquivo principal")
    logger.error("Error message - vai para ambos os arquivos")
    logger.critical("Critical message - vai para ambos os arquivos")
    
    # Verificar se os arquivos foram criados
    if os.path.exists(log_file):
        print(f"✅ Arquivo de log criado: {log_file}")
        with open(log_file, 'r') as f:
            lines = f.readlines()
            print(f"   Contém {len(lines)} linhas")
    
    if os.path.exists(error_log_file):
        print(f"✅ Arquivo de erro criado: {error_log_file}")
        with open(error_log_file, 'r') as f:
            lines = f.readlines()
            print(f"   Contém {len(lines)} linhas")
    
    # Limpar arquivos de teste
    for file_path in [log_file, error_log_file]:
        if os.path.exists(file_path):
            os.remove(file_path)

def example_migration_comparison():
    """Exemplo comparando o sistema antigo com o novo."""
    print("\n=== Comparação: Sistema Antigo vs Novo ===")
    
    print("--- Sistema Antigo (print) ---")
    import datetime
    timestamp = datetime.datetime.now()
    class_name = "ExampleProcessor"
    msg = "Processando batch de mensagens"
    print(f'[{timestamp}] [{class_name}] {msg}')
    
    print("\n--- Sistema Novo (loguru) ---")
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'indexer'))
    import logging_config
    
    logger = logging_config.get_logger("ExampleProcessor")
    logger.info("Processando batch de mensagens")

def main():
    """Executa todos os exemplos."""
    print("🚀 Exemplos de Uso do Sistema de Logging com Loguru\n")
    
    examples = [
        example_basic_usage,
        example_processor_style,
        example_different_formats,
        example_file_logging,
        example_migration_comparison
    ]
    
    for example in examples:
        try:
            example()
        except Exception as e:
            print(f"❌ Erro no exemplo {example.__name__}: {e}")
    
    # Limpar variáveis de ambiente
    for var in ['LOG_FORMAT', 'LOG_LEVEL', 'LOG_FILE', 'ERROR_LOG_FILE']:
        os.environ.pop(var, None)
    
    print("\n✅ Todos os exemplos executados!")

if __name__ == "__main__":
    main()
