#!/usr/bin/env python3
"""
Script de teste simples para verificar apenas a configuração do loguru.
"""

import os
import sys

# Adicionar o diretório do projeto ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_logging_config_only():
    """Testa apenas o módulo de configuração do loguru."""
    print("=== Testando apenas o módulo logging_config ===")
    
    try:
        from indexer.logging_config import get_logger, logger, configure_loguru
        
        # Teste com logger padrão
        logger.info("Teste do logger padrão")
        logger.warning("Teste de warning")
        logger.error("Teste de error")
        logger.success("Teste de success")
        
        # Teste com logger nomeado
        test_logger = get_logger("TestLogger")
        test_logger.info("Teste do logger nomeado")
        test_logger.debug("Teste de debug (pode não aparecer dependendo do nível)")
        
        print("✅ Módulo logging_config funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no módulo logging_config: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_variables():
    """Testa diferentes configurações via variáveis de ambiente."""
    print("\n=== Testando configurações via variáveis de ambiente ===")
    
    try:
        from indexer.logging_config import configure_loguru, get_logger
        
        # Teste com formato simples
        os.environ['LOG_FORMAT'] = 'simple'
        os.environ['LOG_LEVEL'] = 'DEBUG'
        
        # Recarregar a configuração
        configure_loguru()
        
        test_logger = get_logger("EnvTest")
        test_logger.debug("Teste com formato simples e nível DEBUG")
        test_logger.info("Teste de info com formato simples")
        
        # Teste com formato JSON
        os.environ['LOG_FORMAT'] = 'json'
        configure_loguru()
        
        json_logger = get_logger("JSONTest")
        json_logger.info("Teste com formato JSON")
        json_logger.warning("Warning em formato JSON")
        
        print("✅ Configurações via variáveis de ambiente funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro nas configurações de ambiente: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Limpar variáveis de ambiente
        os.environ.pop('LOG_FORMAT', None)
        os.environ.pop('LOG_LEVEL', None)

def test_different_formats():
    """Testa diferentes formatos de log."""
    print("\n=== Testando diferentes formatos ===")
    
    try:
        from indexer.logging_config import configure_loguru, get_logger
        
        formats = ['simple', 'detailed', 'json']
        
        for fmt in formats:
            print(f"\n--- Testando formato: {fmt} ---")
            os.environ['LOG_FORMAT'] = fmt
            os.environ['LOG_LEVEL'] = 'INFO'
            
            configure_loguru()
            
            test_logger = get_logger(f"Format{fmt.title()}Test")
            test_logger.info(f"Teste com formato {fmt}")
            test_logger.warning(f"Warning com formato {fmt}")
            test_logger.success(f"Success com formato {fmt}")
        
        print("\n✅ Todos os formatos funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro nos formatos: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Limpar variáveis de ambiente
        os.environ.pop('LOG_FORMAT', None)
        os.environ.pop('LOG_LEVEL', None)

def main():
    """Executa todos os testes."""
    print("🧪 Iniciando testes simples da configuração do loguru...\n")
    
    tests = [
        test_logging_config_only,
        test_environment_variables,
        test_different_formats
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print(f"\n📊 Resultados: {sum(results)}/{len(results)} testes passaram")
    
    if all(results):
        print("🎉 Todos os testes passaram! A configuração do loguru está funcionando corretamente.")
        return 0
    else:
        print("⚠️  Alguns testes falharam. Verifique a configuração.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
