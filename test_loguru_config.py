#!/usr/bin/env python3
"""
Script de teste para verificar a configuração do loguru.
"""

import os
import sys

# Adicionar o diretório do projeto ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_logging():
    """Testa a configuração básica do loguru."""
    print("=== Testando configuração básica do loguru ===")
    
    try:
        from indexer.logging_config import get_logger, logger
        
        # Teste com logger padrão
        logger.info("Teste do logger padrão")
        logger.warning("Teste de warning")
        logger.error("Teste de error")
        logger.success("Teste de success")
        
        # Teste com logger nomeado
        test_logger = get_logger("TestLogger")
        test_logger.info("Teste do logger nomeado")
        test_logger.debug("Teste de debug (pode não aparecer dependendo do nível)")
        
        print("✅ Configuração básica do loguru funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro na configuração básica: {e}")
        return False

def test_environment_variables():
    """Testa diferentes configurações via variáveis de ambiente."""
    print("\n=== Testando configurações via variáveis de ambiente ===")
    
    try:
        # Teste com formato simples
        os.environ['LOG_FORMAT'] = 'simple'
        os.environ['LOG_LEVEL'] = 'DEBUG'
        
        # Recarregar a configuração
        from indexer.logging_config import configure_loguru, get_logger
        configure_loguru()
        
        test_logger = get_logger("EnvTest")
        test_logger.debug("Teste com formato simples e nível DEBUG")
        test_logger.info("Teste de info com formato simples")
        
        # Teste com formato JSON
        os.environ['LOG_FORMAT'] = 'json'
        configure_loguru()
        
        json_logger = get_logger("JSONTest")
        json_logger.info("Teste com formato JSON")
        json_logger.warning("Warning em formato JSON")
        
        print("✅ Configurações via variáveis de ambiente funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro nas configurações de ambiente: {e}")
        return False
    finally:
        # Limpar variáveis de ambiente
        os.environ.pop('LOG_FORMAT', None)
        os.environ.pop('LOG_LEVEL', None)

def test_processor_logging():
    """Testa o logging em um processador mock."""
    print("\n=== Testando logging em processador mock ===")
    
    try:
        # Criar um processador mock para teste
        class MockProcessor:
            def __init__(self):
                from indexer.logging_config import get_logger
                self.logger = get_logger(self.__class__.__name__)
            
            def log(self, msg, level="INFO", error=False):
                """Mock do método log do AbstractProcessor."""
                if error:
                    level = "ERROR"
                getattr(self.logger, level.lower())(msg)
            
            def test_logging(self):
                self.log("Teste de log normal")
                self.log("Teste de warning", level="WARNING")
                self.log("Teste de error", error=True)
                self.log("Teste de success", level="SUCCESS")
        
        processor = MockProcessor()
        processor.test_logging()
        
        print("✅ Logging em processador funcionando!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no logging do processador: {e}")
        return False

def main():
    """Executa todos os testes."""
    print("🧪 Iniciando testes da configuração do loguru...\n")
    
    tests = [
        test_basic_logging,
        test_environment_variables,
        test_processor_logging
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print(f"\n📊 Resultados: {sum(results)}/{len(results)} testes passaram")
    
    if all(results):
        print("🎉 Todos os testes passaram! A configuração do loguru está funcionando corretamente.")
        return 0
    else:
        print("⚠️  Alguns testes falharam. Verifique a configuração.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
